const crypto = require('crypto');
const fs = require('fs');
const https = require('https');
const vm = require('vm');

class SecureLoader {
    constructor() {
        this.wasmModule = null;
        this.isInitialized = false;
        this.encryptedData = null;
    }

    async initialize() {
        console.log('🔧 Initializing Secure Loader...');

        try {
            // Load Secure WASM module
            const SecureDecryptModule = require('./secure-decrypt.js');
            this.wasmModule = await SecureDecryptModule();

            console.log('✅ Secure WASM module loaded successfully');
            this.isInitialized = true;

        } catch (error) {
            console.error('❌ WASM initialization failed:', error.message);
            console.log('⚠️ Falling back to JavaScript-only mode');
            this.isInitialized = false;
        }
    }

    async loadFromCloudflare() {
        console.log('☁️ Loading encrypted data from Cloudflare...');

        return new Promise((resolve, reject) => {
            const url = 'https://mute-frog-97c5.YOUR-SUBDOMAIN.workers.dev';

            const options = {
                headers: {
                    'X-Auth-Token': 'streamtok-secure-2024'
                }
            };

            https.get(url, options, (res) => {
                if (res.statusCode !== 200) {
                    reject(new Error(`Failed to load: ${res.statusCode}`));
                    return;
                }

                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    try {
                        this.encryptedData = data;
                        console.log('✅ Encrypted data loaded successfully');
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                });
            }).on('error', reject);
        });
    }

    async loadAndDecrypt() {
        console.log('🔓 Starting secure decryption process...');

        if (!this.encryptedData) {
            throw new Error('Missing encrypted data');
        }

        if (this.isInitialized && this.wasmModule) {
            try {
                // استخدام WASM لفك التشفير مع المفاتيح المدمجة
                const decryptFunc = this.wasmModule.cwrap('decrypt_data', 'string', ['string']);

                // فك تشفير البيانات مباشرة باستخدام WASM
                const decrypted = decryptFunc(this.encryptedData);

                console.log(`✅ Decryption successful: ${decrypted.length} characters`);
                return decrypted;

            } catch (error) {
                console.error('❌ WASM decryption failed:', error.message);

                // استخدام Node.js crypto كخطة بديلة
                return this.fallbackDecryption();
            }
        } else {
            // استخدام Node.js crypto كخطة بديلة
            return this.fallbackDecryption();
        }
    }

    async fallbackDecryption() {
        console.log('⚠️ Using fallback decryption method...');

        try {
            // استخدام مفاتيح مشفرة في الكود كخطة بديلة
            const encryptedKeyHex = '2a7e151628aed2a6abf7158809cf4f3c2a7e151628aed2a6abf7158809cf4f3c';
            const encryptedIVHex = '5a678b5d3a1f9c7e5a678b5d3a1f9c7e';
            const masterKeyHex = '0123456789abcdefedcba9876543210';

            // فك تشفير المفاتيح
            const encryptedKey = Buffer.from(encryptedKeyHex, 'hex');
            const encryptedIV = Buffer.from(encryptedIVHex, 'hex');
            const masterKey = Buffer.from(masterKeyHex, 'hex');

            const key = Buffer.alloc(32);
            const iv = Buffer.alloc(16);

            for (let i = 0; i < 32; i++) {
                key[i] = encryptedKey[i] ^ masterKey[i % 16];
            }

            for (let i = 0; i < 16; i++) {
                iv[i] = encryptedIV[i] ^ masterKey[i % 16];
            }

            // فك تشفير البيانات
            const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
            let decrypted = decipher.update(this.encryptedData, 'base64', 'utf8');
            decrypted += decipher.final('utf8');

            console.log(`✅ Fallback decryption successful: ${decrypted.length} characters`);
            return decrypted;

        } catch (error) {
            console.error('❌ Fallback decryption failed:', error.message);
            return null;
        }
    }

    async executeInMemory(code) {
        console.log('⚡ Executing code in memory...');
        console.log('=====================================');

        try {
            // تغيير المنفذ في الكود قبل التنفيذ
            const modifiedCode = code.replace(
                /const PORT = process\.env\.PORT \|\| 3000;/g,
                'const PORT = process.env.PORT || 3001;'
            );

            console.log('🔧 تم تغيير المنفذ من 3000 إلى 3001');

            // استخدام vm module بدلاً من eval لحل مشاكل require والمسارات
            const context = {
                require: require,
                module: module,
                exports: exports,
                __dirname: __dirname,
                __filename: __filename,
                global: global,
                process: process,
                console: console,
                Buffer: Buffer,
                setTimeout: setTimeout,
                setInterval: setInterval,
                clearTimeout: clearTimeout,
                clearInterval: clearInterval
            };

            // إنشاء context آمن للتنفيذ
            vm.createContext(context);

            // تنفيذ الكود في الـ context الجديد
            vm.runInContext(modifiedCode, context);

            console.log('✅ Code executed successfully in secure context');

        } catch (error) {
            console.error('❌ Execution failed:', error.message);
            console.error('Stack trace:', error.stack);
        }
    }
}

// Main execution
async function main() {
    console.log('🚀 Secure Loader Starting...');

    const loader = new SecureLoader();
    await loader.initialize();

    try {
        // Load encrypted data from Cloudflare
        await loader.loadFromCloudflare();

        // Decrypt
        const decryptedCode = await loader.loadAndDecrypt();

        if (decryptedCode && decryptedCode.includes('require') && decryptedCode.includes('express')) {
            console.log('🎯 Code verification passed - executing...');
            await loader.executeInMemory(decryptedCode);
        } else {
            console.log('❌ Code verification failed');
        }
    } catch (error) {
        console.error('❌ Failed to load from Cloudflare:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { SecureLoader };
