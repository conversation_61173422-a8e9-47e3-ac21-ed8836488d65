export default {
  async fetch(request, env) {
    try {
      const FILE_URL = "https://encrypted-files.pages.dev/index.encrypted";
      
      // المفتاح الأساسي من Secrets
      const BASE_SECRET = env.SECRET_KEY;
      if (!BASE_SECRET) {
        return new Response("Server misconfigured", { status: 500 });
      }
      
      // إنشاء مفتاح ديناميكي يتغير كل ساعة
      const currentHour = Math.floor(Date.now() / (1000 * 60 * 60));
      const dynamicKey = BASE_SECRET + "_" + currentHour;
      
      // التحقق من المفتاح
      const url = new URL(request.url);
      const providedKey = url.searchParams.get("key");
      
      if (!providedKey || providedKey !== dynamicKey) {
        return new Response("Access Denied", { status: 403 });
      }
      
      // تحميل وإرجاع الملف
      const fileResponse = await fetch(FILE_URL);
      
      if (!fileResponse.ok) {
        return new Response("File not found", { status: 404 });
      }

      return new Response(fileResponse.body, {
        headers: {
          "Content-Type": "application/octet-stream",
          "Content-Disposition": "attachment; filename=index.encrypted",
          "Access-Control-Allow-Origin": "*",
          "Cache-Control": "no-store, no-cache, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      });
      
    } catch (error) {
      return new Response("Internal Server Error", { status: 500 });
    }
  }
};
