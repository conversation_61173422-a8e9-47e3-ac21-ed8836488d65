export default {
  async fetch(request, env) {
    try {
      // رابط الملف المخزن في Cloudflare Pages
      const FILE_URL = "https://encrypted-files.pages.dev/index.encrypted";

      // مفتاح سري للحماية - يجب أن يكون في Secrets فقط
      const SECRET_KEY = env.SECRET_KEY;
      if (!SECRET_KEY) {
        return new Response("Server misconfigured", { status: 500 });
      }

      // التحقق من المفتاح السري
      const url = new URL(request.url);
      const providedKey = url.searchParams.get("key");

      if (providedKey !== SECRET_KEY) {
        return new Response("Access Denied", { status: 403 });
      }

      // تحميل الملف من Pages
      const fileResponse = await fetch(FILE_URL);

      if (!fileResponse.ok) {
        return new Response("File not found", { status: 404 });
      }

      // إرجاع الملف للمستخدم مع منع التخزين المؤقت
      return new Response(fileResponse.body, {
        headers: {
          "Content-Type": "application/octet-stream",
          "Content-Disposition": "attachment; filename=index.encrypted",
          "Access-Control-Allow-Origin": "*",
          "Cache-Control": "no-store, no-cache, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      });

    } catch (error) {
      return new Response("Error: " + error.message, { status: 500 });
    }
  }
};
