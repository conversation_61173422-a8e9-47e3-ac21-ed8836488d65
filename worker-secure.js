export default {
  async fetch(request, env) {
    try {
      // رابط الملف المخزن في Cloudflare Pages
      const FILE_URL = "https://encrypted-files.pages.dev/index.encrypted";
      
      // مفتاح سري للحماية
      const SECRET_KEY = env.SECRET_KEY || "streamtok-secure-2024";
      
      // التحقق من المفتاح السري
      const url = new URL(request.url);
      const providedKey = url.searchParams.get("key");
      
      if (providedKey !== SECRET_KEY) {
        return new Response("Access Denied", { status: 403 });
      }
      
      // تحميل الملف من Pages
      const fileResponse = await fetch(FILE_URL);

      if (!fileResponse.ok) {
        return new Response("File not found", { status: 404 });
      }

      // إرجاع الملف للمستخدم
      return new Response(fileResponse.body, {
        headers: {
          "Content-Type": "application/octet-stream",
          "Content-Disposition": "attachment; filename=index.encrypted",
          "Access-Control-Allow-Origin": "*"
        }
      });
      
    } catch (error) {
      return new Response("Error: " + error.message, { status: 500 });
    }
  }
};
