@echo off
echo 🔧 Building Secure WASM module...

REM Check if emcc is available
where emcc >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Emscripten not found!
    echo Please install Emscripten first:
    echo https://emscripten.org/docs/getting_started/downloads.html
    pause
    exit /b 1
)

echo ✅ Emscripten found, compiling...

REM Compile C to WASM
emcc secure-decrypt-fixed.c -o secure-decrypt.js ^
    -s EXPORTED_FUNCTIONS="['_decrypt_data', '_decrypt_keys', '_get_key_hex', '_get_iv_hex', '_free_memory', '_malloc', '_free']" ^
    -s EXPORTED_RUNTIME_METHODS="['ccall', 'cwrap', 'UTF8ToString', 'stringToUTF8']" ^
    -s ALLOW_MEMORY_GROWTH=1 ^
    -s MODULARIZE=1 ^
    -s EXPORT_NAME="SecureDecryptModule" ^
    -O2

if %ERRORLEVEL% equ 0 (
    echo ✅ Secure WASM module built successfully!
    echo 📁 Files created:
    echo   - secure-decrypt.js
    echo   - secure-decrypt.wasm
    echo.
    echo 🎯 Next: Update the JavaScript loader to use the secure WASM module
) else (
    echo ❌ Build failed!
)

pause
