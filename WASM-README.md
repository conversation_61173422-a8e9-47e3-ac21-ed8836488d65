# 🔧 WASM Module للتشفير

## المتطلبات:
1. **Emscripten SDK** - لتجميع C إلى WASM
   - تحميل من: https://emscripten.org/docs/getting_started/downloads.html
   - تثبيت: `git clone https://github.com/emscripten-core/emsdk.git`

## خطوات البناء:

### 1. تثبيت Emscripten:
```bash
# Clone emsdk
git clone https://github.com/emscripten-core/emsdk.git
cd emsdk

# Install latest
emsdk install latest
emsdk activate latest

# Add to PATH
emsdk_env.bat  # Windows
source ./emsdk_env.sh  # Linux/Mac
```

### 2. بناء WASM Module:
```bash
# Windows
build-wasm.bat

# Linux/Mac
emcc decrypt.c -o decrypt.js [options...]
```

### 3. اختبار WASM:
```bash
node test-wasm.js
```

## الملفات:
- `decrypt.c` - كود C للتشفير
- `build-wasm.bat` - سكريبت البناء
- `test-wasm.js` - اختبار WASM
- `decrypt.js` - WASM module (مُنتج)
- `decrypt.wasm` - WASM binary (مُنتج)

## المرحلة التالية:
- تحسين التشفير لـ AES-256-CBC
- دمج مع loader.js
- اختبار مع الملف المشفر الحقيقي