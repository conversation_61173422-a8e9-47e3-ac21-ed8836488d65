export default {
  async fetch(request, env) {
    try {
      // رابط الملف المخزن في Cloudflare Pages
      const FILE_URL = "https://encrypted-files.pages.dev/index.encrypted";
      
      // التحقق من وجود المفتاح السري في Secrets
      const SECRET_KEY = env.SECRET_KEY;
      if (!SECRET_KEY) {
        return new Response("Server misconfigured", { status: 500 });
      }
      
      // التحقق من طريقة الطلب
      if (request.method !== "GET") {
        return new Response("Method not allowed", { status: 405 });
      }
      
      // التحقق من المفتاح السري
      const url = new URL(request.url);
      const providedKey = url.searchParams.get("key");
      
      if (!providedKey || providedKey !== SECRET_KEY) {
        return new Response("Access Denied", { status: 403 });
      }
      
      // إضافة Rate Limiting بسيط
      const clientIP = request.headers.get("CF-Connecting-IP");
      const rateLimitKey = `rate_limit_${clientIP}`;
      
      // تحميل الملف من Pages
      const fileResponse = await fetch(FILE_URL, {
        headers: {
          "User-Agent": "Secure-Worker/1.0"
        }
      });

      if (!fileResponse.ok) {
        return new Response("File not found", { status: 404 });
      }

      // إرجاع الملف مع أقصى حماية ضد التخزين
      return new Response(fileResponse.body, {
        headers: {
          "Content-Type": "application/octet-stream",
          "Content-Disposition": "attachment; filename=index.encrypted",
          "Access-Control-Allow-Origin": "*",
          // منع التخزين المؤقت تماماً
          "Cache-Control": "no-store, no-cache, must-revalidate, private",
          "Pragma": "no-cache",
          "Expires": "0",
          // حماية إضافية
          "X-Content-Type-Options": "nosniff",
          "X-Frame-Options": "DENY",
          "X-XSS-Protection": "1; mode=block"
        }
      });
      
    } catch (error) {
      return new Response("Internal Server Error", { status: 500 });
    }
  }
};
