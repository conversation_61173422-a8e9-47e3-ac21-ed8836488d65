#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <emscripten.h>

// Very simple test function
EMSCRIPTEN_KEEPALIVE
char* test_wasm() {
    char* result = (char*)malloc(50);
    strcpy(result, "WASM is working!");
    return result;
}

// Simple string length test
EMSCRIPTEN_KEEPALIVE
int get_string_length(const char* input) {
    if (!input) return -1;
    return strlen(input);
}

// Free memory function
EMSCRIPTEN_KEEPALIVE
void free_memory(char* ptr) {
    if (ptr) {
        free(ptr);
    }
}