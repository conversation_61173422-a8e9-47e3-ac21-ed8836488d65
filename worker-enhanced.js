export default {
  async fetch(request, env) {
    try {
      // رابط الملف المخزن في Cloudflare Pages
      const FILE_URL = "https://encrypted-files.pages.dev/index.encrypted";
      
      // إضافة مفتاح سري للحماية (اختياري)
      const authHeader = request.headers.get("Authorization");
      const SECRET_KEY = "your-secret-key-here"; // ضع مفتاح سري
      
      // التحقق من المفتاح السري (اختياري)
      if (authHeader !== `Bearer ${SECRET_KEY}`) {
        return new Response("Unauthorized", { status: 401 });
      }
      
      // تحميل الملف من Pages
      const fileResponse = await fetch(FILE_URL, {
        headers: {
          "User-Agent": "Cloudflare-Worker/1.0"
        }
      });

      if (!fileResponse.ok) {
        return new Response("File not found", { status: 404 });
      }

      // إرجاع الملف للمستخدم
      return new Response(fileResponse.body, {
        headers: {
          "Content-Type": "application/octet-stream",
          "Content-Disposition": "attachment; filename=index.encrypted",
          "Access-Control-Allow-Origin": "*",
          "Cache-Control": "public, max-age=3600" // تخزين مؤقت لساعة
        }
      });
      
    } catch (error) {
      return new Response("Error: " + error.message, { status: 500 });
    }
  }
};
